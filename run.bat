@echo off
echo Starting Weather Application...
echo.

REM Compile the application (without FlatLaf in compile classpath)
echo Compiling Java sources...
javac -cp "src" -Xlint:-deprecation src/Main.java src/ui/WeatherUI.java src/model/*.java src/service/WeatherService.java src/utils/PreferenceManager.java -d bin

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed!
    pause
    exit /b 1
)

REM Copy configuration file
echo Copying configuration file...
copy src\config.properties bin\ >nul 2>&1

REM Check if API key is configured
findstr /C:"YOUR_API_KEY_HERE" src\config.properties >nul
if %ERRORLEVEL% EQU 0 (
    echo.
    echo WARNING: API key not configured!
    echo Please edit src\config.properties and set your OpenWeatherMap API key.
    echo The application will use mock data for demonstration.
    echo.
)

REM Run the application
echo Starting Weather Application...
echo.
java -cp "lib/flatlaf-3.6.jar;bin" Main

pause
