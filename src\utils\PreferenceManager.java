package utils;

import java.util.prefs.Preferences;

/**
 * Utility class for managing user preferences
 * Uses Java Preferences API to store settings persistently
 */
public class PreferenceManager {
    private static final String PREF_NODE = "weather_app";
    private static final Preferences prefs = Preferences.userNodeForPackage(PreferenceManager.class).node(PREF_NODE);
    
    // Preference keys
    private static final String KEY_TEMPERATURE_UNIT = "temperature_unit";
    private static final String KEY_LAST_CITY = "last_city";
    private static final String KEY_THEME = "theme";
    private static final String KEY_WINDOW_WIDTH = "window_width";
    private static final String KEY_WINDOW_HEIGHT = "window_height";
    private static final String KEY_WINDOW_X = "window_x";
    private static final String KEY_WINDOW_Y = "window_y";
    private static final String KEY_AUTO_REFRESH = "auto_refresh";
    private static final String KEY_REFRESH_INTERVAL = "refresh_interval";
    
    // Default values
    public static final String DEFAULT_TEMPERATURE_UNIT = "celsius";
    public static final String DEFAULT_CITY = "Paris";
    public static final String DEFAULT_THEME = "light";
    public static final int DEFAULT_WINDOW_WIDTH = 800;
    public static final int DEFAULT_WINDOW_HEIGHT = 600;
    public static final boolean DEFAULT_AUTO_REFRESH = true;
    public static final int DEFAULT_REFRESH_INTERVAL = 300; // 5 minutes in seconds

    // Temperature unit methods
    public static String getTemperatureUnit() {
        return prefs.get(KEY_TEMPERATURE_UNIT, DEFAULT_TEMPERATURE_UNIT);
    }

    public static void setTemperatureUnit(String unit) {
        prefs.put(KEY_TEMPERATURE_UNIT, unit);
    }

    public static boolean isCelsius() {
        return "celsius".equals(getTemperatureUnit());
    }

    public static boolean isFahrenheit() {
        return "fahrenheit".equals(getTemperatureUnit());
    }

    // City methods
    public static String getLastCity() {
        return prefs.get(KEY_LAST_CITY, DEFAULT_CITY);
    }

    public static void setLastCity(String city) {
        prefs.put(KEY_LAST_CITY, city);
    }

    // Theme methods
    public static String getTheme() {
        return prefs.get(KEY_THEME, DEFAULT_THEME);
    }

    public static void setTheme(String theme) {
        prefs.put(KEY_THEME, theme);
    }

    public static boolean isLightTheme() {
        return "light".equals(getTheme());
    }

    public static boolean isDarkTheme() {
        return "dark".equals(getTheme());
    }

    // Window position and size methods
    public static int getWindowWidth() {
        return prefs.getInt(KEY_WINDOW_WIDTH, DEFAULT_WINDOW_WIDTH);
    }

    public static void setWindowWidth(int width) {
        prefs.putInt(KEY_WINDOW_WIDTH, width);
    }

    public static int getWindowHeight() {
        return prefs.getInt(KEY_WINDOW_HEIGHT, DEFAULT_WINDOW_HEIGHT);
    }

    public static void setWindowHeight(int height) {
        prefs.putInt(KEY_WINDOW_HEIGHT, height);
    }

    public static int getWindowX() {
        return prefs.getInt(KEY_WINDOW_X, -1);
    }

    public static void setWindowX(int x) {
        prefs.putInt(KEY_WINDOW_X, x);
    }

    public static int getWindowY() {
        return prefs.getInt(KEY_WINDOW_Y, -1);
    }

    public static void setWindowY(int y) {
        prefs.putInt(KEY_WINDOW_Y, y);
    }

    // Auto refresh methods
    public static boolean isAutoRefreshEnabled() {
        return prefs.getBoolean(KEY_AUTO_REFRESH, DEFAULT_AUTO_REFRESH);
    }

    public static void setAutoRefreshEnabled(boolean enabled) {
        prefs.putBoolean(KEY_AUTO_REFRESH, enabled);
    }

    public static int getRefreshInterval() {
        return prefs.getInt(KEY_REFRESH_INTERVAL, DEFAULT_REFRESH_INTERVAL);
    }

    public static void setRefreshInterval(int intervalSeconds) {
        prefs.putInt(KEY_REFRESH_INTERVAL, intervalSeconds);
    }

    // Utility methods
    public static void clearAllPreferences() {
        try {
            prefs.clear();
        } catch (Exception e) {
            System.err.println("Failed to clear preferences: " + e.getMessage());
        }
    }

    public static double convertTemperature(double celsius, boolean toCelsius) {
        if (toCelsius) {
            return celsius;
        } else {
            // Convert to Fahrenheit
            return (celsius * 9.0 / 5.0) + 32.0;
        }
    }

    public static String getTemperatureSymbol() {
        return isCelsius() ? "°C" : "°F";
    }

    public static String formatTemperature(double celsius) {
        double temp = convertTemperature(celsius, isCelsius());
        return String.format("%.0f%s", temp, getTemperatureSymbol());
    }
}
