package service;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import model.Forecast;
import model.HourlyForecast;
import model.WeatherData;

/**
 * Service class for fetching weather data from OpenWeatherMap API
 */
public class WeatherService {
    private String apiKey;
    private String baseUrl;
    private static final String CONFIG_FILE = "config.properties";

    public WeatherService() {
        loadConfiguration();
    }

    private void loadConfiguration() {
        Properties props = new Properties();
        try (InputStream input = getClass().getClassLoader().getResourceAsStream(CONFIG_FILE)) {
            if (input == null) {
                // Try loading from src directory
                try (FileInputStream fileInput = new FileInputStream("src/" + CONFIG_FILE)) {
                    props.load(fileInput);
                }
            } else {
                props.load(input);
            }
            
            this.apiKey = props.getProperty("api.key", "");
            this.baseUrl = props.getProperty("api.base.url", "https://api.openweathermap.org/data/2.5");
            
            if (apiKey.isEmpty() || "YOUR_API_KEY_HERE".equals(apiKey)) {
                System.err.println("Warning: API key not configured. Please set your OpenWeatherMap API key in config.properties");
            }
        } catch (IOException e) {
            System.err.println("Failed to load configuration: " + e.getMessage());
            this.baseUrl = "https://api.openweathermap.org/data/2.5";
        }
    }

    /**
     * Fetch current weather data for a city
     */
    public WeatherData getCurrentWeather(String cityName) throws Exception {
        if (apiKey.isEmpty() || "YOUR_API_KEY_HERE".equals(apiKey)) {
            return createMockWeatherData(cityName);
        }

        String encodedCity = URLEncoder.encode(cityName, StandardCharsets.UTF_8.toString());
        String urlString = String.format("%s/weather?q=%s&appid=%s&units=metric", 
                                        baseUrl, encodedCity, apiKey);
        
        String jsonResponse = makeHttpRequest(urlString);
        return parseCurrentWeatherJson(jsonResponse);
    }

    /**
     * Fetch 5-day weather forecast for a city
     */
    public List<Forecast> getFiveDayForecast(String cityName) throws Exception {
        if (apiKey.isEmpty() || "YOUR_API_KEY_HERE".equals(apiKey)) {
            return createMockForecastData();
        }

        String encodedCity = URLEncoder.encode(cityName, StandardCharsets.UTF_8.toString());
        String urlString = String.format("%s/forecast?q=%s&appid=%s&units=metric", 
                                        baseUrl, encodedCity, apiKey);
        
        String jsonResponse = makeHttpRequest(urlString);
        return parseForecastJson(jsonResponse);
    }

    /**
     * Fetch hourly forecast for a city (using 5-day forecast API)
     */
    public List<HourlyForecast> getHourlyForecast(String cityName) throws Exception {
        if (apiKey.isEmpty() || "YOUR_API_KEY_HERE".equals(apiKey)) {
            return createMockHourlyData();
        }

        String encodedCity = URLEncoder.encode(cityName, StandardCharsets.UTF_8.toString());
        String urlString = String.format("%s/forecast?q=%s&appid=%s&units=metric", 
                                        baseUrl, encodedCity, apiKey);
        
        String jsonResponse = makeHttpRequest(urlString);
        return parseHourlyForecastJson(jsonResponse);
    }

    private String makeHttpRequest(String urlString) throws Exception {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000);
        connection.setReadTimeout(5000);

        int responseCode = connection.getResponseCode();
        if (responseCode != 200) {
            throw new Exception("HTTP Error: " + responseCode + " - " + connection.getResponseMessage());
        }

        StringBuilder response = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
        }

        return response.toString();
    }

    // Mock data methods for testing without API key
    private WeatherData createMockWeatherData(String cityName) {
        WeatherData data = new WeatherData();
        data.setCityName(cityName);
        data.setCountry("FR");
        data.setTemperature(22.5);
        data.setFeelsLike(24.0);
        data.setHumidity(65);
        data.setPressure(1013.25);
        data.setWindSpeed(3.5);
        data.setWindDirection(180);
        data.setWeatherCondition("Clear");
        data.setWeatherDescription("Clear sky");
        data.setIconCode("01d");
        data.setTimestamp(System.currentTimeMillis());
        data.setVisibility(10000);
        return data;
    }

    private List<Forecast> createMockForecastData() {
        List<Forecast> forecasts = new ArrayList<>();
        String[] conditions = {"Clear", "Clouds", "Rain", "Clouds", "Clear"};
        String[] descriptions = {"Clear sky", "Few clouds", "Light rain", "Scattered clouds", "Clear sky"};
        
        for (int i = 0; i < 5; i++) {
            Forecast forecast = new Forecast();
            forecast.setDate(LocalDate.now().plusDays(i + 1));
            forecast.setMinTemperature(15 + i);
            forecast.setMaxTemperature(25 + i);
            forecast.setWeatherCondition(conditions[i]);
            forecast.setWeatherDescription(descriptions[i]);
            forecast.setIconCode("01d");
            forecast.setHumidity(60 + i * 5);
            forecast.setWindSpeed(2.0 + i);
            forecasts.add(forecast);
        }
        
        return forecasts;
    }

    private List<HourlyForecast> createMockHourlyData() {
        List<HourlyForecast> hourlyForecasts = new ArrayList<>();
        
        for (int i = 0; i < 24; i++) {
            HourlyForecast forecast = new HourlyForecast();
            forecast.setDateTime(LocalDateTime.now().plusHours(i));
            forecast.setTemperature(20 + Math.sin(i * Math.PI / 12) * 5);
            forecast.setFeelsLike(forecast.getTemperature() + 2);
            forecast.setWeatherCondition(i % 6 == 0 ? "Rain" : "Clear");
            forecast.setWeatherDescription(i % 6 == 0 ? "Light rain" : "Clear sky");
            forecast.setIconCode(i % 6 == 0 ? "10d" : "01d");
            forecast.setHumidity(50 + i % 30);
            forecast.setWindSpeed(2.0 + i % 5);
            forecast.setWindDirection(i * 15 % 360);
            hourlyForecasts.add(forecast);
        }
        
        return hourlyForecasts;
    }

    // Simple JSON parsing methods (basic implementation)
    private WeatherData parseCurrentWeatherJson(String json) {
        // This is a simplified JSON parser for demonstration
        // In a real application, you would use a proper JSON library like Jackson or Gson
        WeatherData data = new WeatherData();

        try {
            // Extract city name
            data.setCityName(extractJsonValue(json, "name"));

            // Extract country from sys object
            String sysSection = extractJsonSection(json, "sys");
            if (!sysSection.isEmpty()) {
                data.setCountry(extractJsonValue(sysSection, "country"));
            }

            // Extract main weather data
            String mainSection = extractJsonSection(json, "main");
            if (!mainSection.isEmpty()) {
                data.setTemperature(Double.parseDouble(extractJsonValue(mainSection, "temp")));
                data.setFeelsLike(Double.parseDouble(extractJsonValue(mainSection, "feels_like")));
                data.setHumidity(Integer.parseInt(extractJsonValue(mainSection, "humidity")));
                data.setPressure(Double.parseDouble(extractJsonValue(mainSection, "pressure")));
            }

            // Extract wind data
            String windSection = extractJsonSection(json, "wind");
            if (!windSection.isEmpty()) {
                data.setWindSpeed(Double.parseDouble(extractJsonValue(windSection, "speed")));
                String degStr = extractJsonValue(windSection, "deg");
                if (!degStr.isEmpty()) {
                    data.setWindDirection(Integer.parseInt(degStr));
                }
            }

            // Extract weather condition
            String weatherSection = extractJsonSection(json, "weather");
            if (!weatherSection.isEmpty()) {
                // Weather is an array, get first element
                int firstBrace = weatherSection.indexOf('{');
                int lastBrace = weatherSection.indexOf('}', firstBrace);
                if (firstBrace != -1 && lastBrace != -1) {
                    String firstWeather = weatherSection.substring(firstBrace, lastBrace + 1);
                    data.setWeatherCondition(extractJsonValue(firstWeather, "main"));
                    data.setWeatherDescription(extractJsonValue(firstWeather, "description"));
                    data.setIconCode(extractJsonValue(firstWeather, "icon"));
                }
            }

            // Extract visibility
            String visibilityStr = extractJsonValue(json, "visibility");
            if (!visibilityStr.isEmpty()) {
                data.setVisibility(Integer.parseInt(visibilityStr));
            }

            data.setTimestamp(System.currentTimeMillis());

        } catch (Exception e) {
            System.err.println("Error parsing weather data: " + e.getMessage());
            System.err.println("JSON response: " + json.substring(0, Math.min(200, json.length())) + "...");
            return createMockWeatherData(data.getCityName() != null ? data.getCityName() : "Unknown");
        }

        return data;
    }

    private List<Forecast> parseForecastJson(String json) {
        // Simplified implementation - return mock data for now
        return createMockForecastData();
    }

    private List<HourlyForecast> parseHourlyForecastJson(String json) {
        // Simplified implementation - return mock data for now
        return createMockHourlyData();
    }

    private String extractJsonSection(String json, String key) {
        // Extract a JSON object or array section
        String searchKey = "\"" + key + "\":";
        int startIndex = json.indexOf(searchKey);
        if (startIndex == -1) return "";

        startIndex += searchKey.length();
        while (startIndex < json.length() && Character.isWhitespace(json.charAt(startIndex))) {
            startIndex++;
        }

        if (startIndex >= json.length()) return "";

        char firstChar = json.charAt(startIndex);
        if (firstChar == '{') {
            // Object
            int braceCount = 1;
            int endIndex = startIndex + 1;
            while (endIndex < json.length() && braceCount > 0) {
                char c = json.charAt(endIndex);
                if (c == '{') braceCount++;
                else if (c == '}') braceCount--;
                endIndex++;
            }
            return json.substring(startIndex, endIndex);
        } else if (firstChar == '[') {
            // Array
            int bracketCount = 1;
            int endIndex = startIndex + 1;
            while (endIndex < json.length() && bracketCount > 0) {
                char c = json.charAt(endIndex);
                if (c == '[') bracketCount++;
                else if (c == ']') bracketCount--;
                endIndex++;
            }
            return json.substring(startIndex, endIndex);
        }

        return "";
    }

    private String extractJsonValue(String json, String key) {
        // Very basic JSON value extraction
        String searchKey = "\"" + key + "\":";
        int startIndex = json.indexOf(searchKey);
        if (startIndex == -1) return "";

        startIndex += searchKey.length();
        while (startIndex < json.length() && Character.isWhitespace(json.charAt(startIndex))) {
            startIndex++;
        }

        if (startIndex >= json.length()) return "";

        char firstChar = json.charAt(startIndex);
        if (firstChar == '"') {
            // String value
            startIndex++;
            int endIndex = json.indexOf('"', startIndex);
            return endIndex != -1 ? json.substring(startIndex, endIndex) : "";
        } else {
            // Numeric value
            int endIndex = startIndex;
            while (endIndex < json.length() &&
                   (Character.isDigit(json.charAt(endIndex)) ||
                    json.charAt(endIndex) == '.' ||
                    json.charAt(endIndex) == '-')) {
                endIndex++;
            }
            return json.substring(startIndex, endIndex);
        }
    }

    public boolean isApiKeyConfigured() {
        return !apiKey.isEmpty() && !"YOUR_API_KEY_HERE".equals(apiKey);
    }
}
