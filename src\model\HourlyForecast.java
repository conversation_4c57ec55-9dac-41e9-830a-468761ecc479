package model;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Model class representing hourly weather forecast
 */
public class HourlyForecast {
    private LocalDateTime dateTime;
    private double temperature;
    private double feelsLike;
    private String weatherCondition;
    private String weatherDescription;
    private String iconCode;
    private int humidity;
    private double windSpeed;
    private int windDirection;
    private double pressure;
    private double pop; // Probability of precipitation
    private double visibility;

    // Constructors
    public HourlyForecast() {}

    public HourlyForecast(LocalDateTime dateTime, double temperature, 
                         String weatherCondition, String weatherDescription) {
        this.dateTime = dateTime;
        this.temperature = temperature;
        this.weatherCondition = weatherCondition;
        this.weatherDescription = weatherDescription;
    }

    // Getters and Setters
    public LocalDateTime getDateTime() {
        return dateTime;
    }

    public void setDateTime(LocalDateTime dateTime) {
        this.dateTime = dateTime;
    }

    public double getTemperature() {
        return temperature;
    }

    public void setTemperature(double temperature) {
        this.temperature = temperature;
    }

    public double getFeelsLike() {
        return feelsLike;
    }

    public void setFeelsLike(double feelsLike) {
        this.feelsLike = feelsLike;
    }

    public String getWeatherCondition() {
        return weatherCondition;
    }

    public void setWeatherCondition(String weatherCondition) {
        this.weatherCondition = weatherCondition;
    }

    public String getWeatherDescription() {
        return weatherDescription;
    }

    public void setWeatherDescription(String weatherDescription) {
        this.weatherDescription = weatherDescription;
    }

    public String getIconCode() {
        return iconCode;
    }

    public void setIconCode(String iconCode) {
        this.iconCode = iconCode;
    }

    public int getHumidity() {
        return humidity;
    }

    public void setHumidity(int humidity) {
        this.humidity = humidity;
    }

    public double getWindSpeed() {
        return windSpeed;
    }

    public void setWindSpeed(double windSpeed) {
        this.windSpeed = windSpeed;
    }

    public int getWindDirection() {
        return windDirection;
    }

    public void setWindDirection(int windDirection) {
        this.windDirection = windDirection;
    }

    public double getPressure() {
        return pressure;
    }

    public void setPressure(double pressure) {
        this.pressure = pressure;
    }

    public double getPop() {
        return pop;
    }

    public void setPop(double pop) {
        this.pop = pop;
    }

    public double getVisibility() {
        return visibility;
    }

    public void setVisibility(double visibility) {
        this.visibility = visibility;
    }

    // Utility methods
    public String getFormattedTime() {
        return dateTime.format(DateTimeFormatter.ofPattern("HH:mm"));
    }

    public String getFormattedDateTime() {
        return dateTime.format(DateTimeFormatter.ofPattern("MMM dd, HH:mm"));
    }

    public String getWindDirectionText() {
        if (windDirection >= 337.5 || windDirection < 22.5) return "N";
        else if (windDirection < 67.5) return "NE";
        else if (windDirection < 112.5) return "E";
        else if (windDirection < 157.5) return "SE";
        else if (windDirection < 202.5) return "S";
        else if (windDirection < 247.5) return "SW";
        else if (windDirection < 292.5) return "W";
        else return "NW";
    }

    @Override
    public String toString() {
        return String.format("HourlyForecast{time=%s, temp=%.1f°, condition='%s'}", 
                           getFormattedTime(), temperature, weatherCondition);
    }
}
