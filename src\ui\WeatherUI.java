package ui;

// FlatLaf imports removed to avoid compilation dependency
import model.WeatherData;
import model.Forecast;
import model.HourlyForecast;
import service.WeatherService;
import utils.PreferenceManager;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import java.awt.*;

import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.util.List;

/**
 * Main UI class for the Weather Application
 * Modern Swing interface with FlatLaf styling
 */
public class WeatherUI extends JFrame {
    private WeatherService weatherService;
    private JTextField citySearchField;
    private JButton searchButton;
    private JButton refreshButton;
    private JToggleButton themeToggleButton;
    private JToggleButton unitToggleButton;
    
    // Current weather components
    private JLabel cityLabel;
    private JLabel temperatureLabel;
    private JLabel conditionLabel;
    private JLabel feelsLikeLabel;
    private JLabel humidityLabel;
    private JLabel windLabel;
    private JLabel pressureLabel;
    
    // Forecast panels
    private JPanel forecastPanel;
    private JPanel hourlyPanel;
    
    // Status components
    private JLabel statusLabel;
    private JProgressBar loadingBar;
    
    private String currentCity;

    public WeatherUI() {
        this.weatherService = new WeatherService();
        this.currentCity = PreferenceManager.getLastCity();
        
        initializeComponents();
        setupLayout();
        setupEventHandlers();
        restoreWindowState();
        
        // Load initial weather data
        loadWeatherData(currentCity);
    }

    private void initializeComponents() {
        setTitle("Weather App - Modern Java Weather Application");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        
        // Search components
        citySearchField = new JTextField(currentCity, 20);
        citySearchField.setToolTipText("Enter city name");
        searchButton = new JButton("Search");
        refreshButton = new JButton("Refresh");
        
        // Toggle buttons
        themeToggleButton = new JToggleButton(PreferenceManager.isDarkTheme() ? "Light" : "Dark");
        themeToggleButton.setSelected(PreferenceManager.isDarkTheme());
        
        unitToggleButton = new JToggleButton(PreferenceManager.isCelsius() ? "°F" : "°C");
        unitToggleButton.setSelected(PreferenceManager.isFahrenheit());
        
        // Current weather labels
        cityLabel = new JLabel("Loading...");
        cityLabel.setFont(new Font(Font.SANS_SERIF, Font.BOLD, 24));
        
        temperatureLabel = new JLabel("--°");
        temperatureLabel.setFont(new Font(Font.SANS_SERIF, Font.BOLD, 48));
        
        conditionLabel = new JLabel("--");
        conditionLabel.setFont(new Font(Font.SANS_SERIF, Font.PLAIN, 16));
        
        feelsLikeLabel = new JLabel("Feels like: --°");
        humidityLabel = new JLabel("Humidity: --%");
        windLabel = new JLabel("Wind: -- km/h");
        pressureLabel = new JLabel("Pressure: -- hPa");
        
        // Forecast panels
        forecastPanel = new JPanel(new GridLayout(1, 5, 10, 0));
        forecastPanel.setBorder(BorderFactory.createTitledBorder("5-Day Forecast"));
        
        hourlyPanel = new JPanel();
        hourlyPanel.setLayout(new BoxLayout(hourlyPanel, BoxLayout.X_AXIS));
        hourlyPanel.setBorder(BorderFactory.createTitledBorder("24-Hour Forecast"));
        
        // Status components
        statusLabel = new JLabel("Ready");
        loadingBar = new JProgressBar();
        loadingBar.setIndeterminate(true);
        loadingBar.setVisible(false);
    }

    private void setupLayout() {
        setLayout(new BorderLayout(10, 10));
        
        // Top panel - Search and controls
        JPanel topPanel = new JPanel(new FlowLayout(FlowLayout.LEFT));
        topPanel.add(new JLabel("City:"));
        topPanel.add(citySearchField);
        topPanel.add(searchButton);
        topPanel.add(refreshButton);
        topPanel.add(Box.createHorizontalStrut(20));
        topPanel.add(new JLabel("Theme:"));
        topPanel.add(themeToggleButton);
        topPanel.add(new JLabel("Unit:"));
        topPanel.add(unitToggleButton);
        
        // Center panel - Main weather info
        JPanel centerPanel = new JPanel(new BorderLayout(10, 10));
        centerPanel.setBorder(new EmptyBorder(20, 20, 20, 20));
        
        // Current weather panel
        JPanel currentWeatherPanel = new JPanel(new BorderLayout());
        currentWeatherPanel.setBorder(BorderFactory.createTitledBorder("Current Weather"));
        
        JPanel currentInfoPanel = new JPanel(new GridBagLayout());
        GridBagConstraints gbc = new GridBagConstraints();
        
        gbc.gridx = 0; gbc.gridy = 0; gbc.gridwidth = 2; gbc.anchor = GridBagConstraints.CENTER;
        currentInfoPanel.add(cityLabel, gbc);
        
        gbc.gridy = 1; gbc.insets = new Insets(10, 0, 10, 0);
        currentInfoPanel.add(temperatureLabel, gbc);
        
        gbc.gridy = 2; gbc.insets = new Insets(0, 0, 20, 0);
        currentInfoPanel.add(conditionLabel, gbc);
        
        gbc.gridwidth = 1; gbc.anchor = GridBagConstraints.WEST;
        gbc.gridx = 0; gbc.gridy = 3; gbc.insets = new Insets(5, 0, 5, 20);
        currentInfoPanel.add(feelsLikeLabel, gbc);
        
        gbc.gridx = 1;
        currentInfoPanel.add(humidityLabel, gbc);
        
        gbc.gridx = 0; gbc.gridy = 4;
        currentInfoPanel.add(windLabel, gbc);
        
        gbc.gridx = 1;
        currentInfoPanel.add(pressureLabel, gbc);
        
        currentWeatherPanel.add(currentInfoPanel, BorderLayout.CENTER);
        
        centerPanel.add(currentWeatherPanel, BorderLayout.NORTH);
        centerPanel.add(forecastPanel, BorderLayout.CENTER);
        
        // Hourly forecast in a scroll pane
        JScrollPane hourlyScrollPane = new JScrollPane(hourlyPanel);
        hourlyScrollPane.setPreferredSize(new Dimension(0, 150));
        hourlyScrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
        hourlyScrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_NEVER);
        centerPanel.add(hourlyScrollPane, BorderLayout.SOUTH);
        
        // Bottom panel - Status
        JPanel bottomPanel = new JPanel(new BorderLayout());
        bottomPanel.setBorder(new EmptyBorder(5, 10, 5, 10));
        bottomPanel.add(statusLabel, BorderLayout.WEST);
        bottomPanel.add(loadingBar, BorderLayout.CENTER);
        
        add(topPanel, BorderLayout.NORTH);
        add(centerPanel, BorderLayout.CENTER);
        add(bottomPanel, BorderLayout.SOUTH);
    }

    private void setupEventHandlers() {
        // Search button
        searchButton.addActionListener(_ -> {
            String city = citySearchField.getText().trim();
            if (!city.isEmpty()) {
                loadWeatherData(city);
            }
        });

        // Enter key in search field
        citySearchField.addActionListener(_ -> searchButton.doClick());

        // Refresh button
        refreshButton.addActionListener(_ -> loadWeatherData(currentCity));

        // Theme toggle
        themeToggleButton.addActionListener(_ -> toggleTheme());

        // Unit toggle
        unitToggleButton.addActionListener(_ -> toggleTemperatureUnit());
        
        // Window closing event
        addWindowListener(new WindowAdapter() {
            @Override
            public void windowClosing(WindowEvent e) {
                saveWindowState();
                PreferenceManager.setLastCity(currentCity);
            }
        });
    }

    private void loadWeatherData(String city) {
        setLoading(true);
        statusLabel.setText("Loading weather data for " + city + "...");
        
        // Use SwingWorker for background loading
        SwingWorker<Void, Void> worker = new SwingWorker<Void, Void>() {
            private WeatherData currentWeather;
            private List<Forecast> forecasts;
            private List<HourlyForecast> hourlyForecasts;
            private Exception error;
            
            @Override
            protected Void doInBackground() throws Exception {
                try {
                    currentWeather = weatherService.getCurrentWeather(city);
                    forecasts = weatherService.getFiveDayForecast(city);
                    hourlyForecasts = weatherService.getHourlyForecast(city);
                } catch (Exception e) {
                    error = e;
                }
                return null;
            }
            
            @Override
            protected void done() {
                setLoading(false);
                
                if (error != null) {
                    statusLabel.setText("Error: " + error.getMessage());
                    JOptionPane.showMessageDialog(WeatherUI.this, 
                        "Failed to load weather data: " + error.getMessage(), 
                        "Error", JOptionPane.ERROR_MESSAGE);
                } else {
                    updateWeatherDisplay(currentWeather, forecasts, hourlyForecasts);
                    currentCity = city;
                    statusLabel.setText("Weather data updated successfully");
                }
            }
        };
        
        worker.execute();
    }

    private void updateWeatherDisplay(WeatherData weather, List<Forecast> forecasts, 
                                    List<HourlyForecast> hourlyForecasts) {
        if (weather != null) {
            cityLabel.setText(weather.getFormattedLocation());
            updateTemperatureDisplay(weather);
            conditionLabel.setText(weather.getWeatherDescription());
            feelsLikeLabel.setText("Feels like: " + PreferenceManager.formatTemperature(weather.getFeelsLike()));
            humidityLabel.setText("Humidity: " + weather.getHumidity() + "%");
            windLabel.setText(String.format("Wind: %.1f km/h %s", weather.getWindSpeed(), weather.getWindDirectionText()));
            pressureLabel.setText(String.format("Pressure: %.1f hPa", weather.getPressure()));
        }
        
        updateForecastDisplay(forecasts);
        updateHourlyDisplay(hourlyForecasts);
    }

    private void updateTemperatureDisplay(WeatherData weather) {
        temperatureLabel.setText(PreferenceManager.formatTemperature(weather.getTemperature()));
    }

    private void updateForecastDisplay(List<Forecast> forecasts) {
        forecastPanel.removeAll();
        
        if (forecasts != null) {
            for (Forecast forecast : forecasts) {
                JPanel dayPanel = createForecastDayPanel(forecast);
                forecastPanel.add(dayPanel);
            }
        }
        
        forecastPanel.revalidate();
        forecastPanel.repaint();
    }

    private JPanel createForecastDayPanel(Forecast forecast) {
        JPanel panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.Y_AXIS));
        panel.setBorder(new EmptyBorder(10, 5, 10, 5));
        
        JLabel dayLabel = new JLabel(forecast.getDayOfWeek());
        dayLabel.setFont(new Font(Font.SANS_SERIF, Font.BOLD, 12));
        dayLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        
        JLabel dateLabel = new JLabel(forecast.getFormattedDate());
        dateLabel.setFont(new Font(Font.SANS_SERIF, Font.PLAIN, 10));
        dateLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        
        JLabel conditionLabel = new JLabel(forecast.getWeatherCondition());
        conditionLabel.setFont(new Font(Font.SANS_SERIF, Font.PLAIN, 11));
        conditionLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        
        JLabel tempLabel = new JLabel(String.format("%.0f° / %.0f°", 
            PreferenceManager.convertTemperature(forecast.getMinTemperature(), PreferenceManager.isCelsius()),
            PreferenceManager.convertTemperature(forecast.getMaxTemperature(), PreferenceManager.isCelsius())));
        tempLabel.setFont(new Font(Font.SANS_SERIF, Font.BOLD, 12));
        tempLabel.setAlignmentX(Component.CENTER_ALIGNMENT);
        
        panel.add(dayLabel);
        panel.add(dateLabel);
        panel.add(Box.createVerticalStrut(5));
        panel.add(conditionLabel);
        panel.add(Box.createVerticalStrut(5));
        panel.add(tempLabel);
        
        return panel;
    }

    private void updateHourlyDisplay(List<HourlyForecast> hourlyForecasts) {
        hourlyPanel.removeAll();

        if (hourlyForecasts != null) {
            for (int i = 0; i < Math.min(24, hourlyForecasts.size()); i++) {
                HourlyForecast forecast = hourlyForecasts.get(i);
                JPanel hourPanel = createHourlyForecastPanel(forecast);
                hourlyPanel.add(hourPanel);

                if (i < Math.min(23, hourlyForecasts.size() - 1)) {
                    hourlyPanel.add(Box.createHorizontalStrut(10));
                }
            }
        }

        hourlyPanel.revalidate();
        hourlyPanel.repaint();
    }

    private JPanel createHourlyForecastPanel(HourlyForecast forecast) {
        JPanel panel = new JPanel();
        panel.setLayout(new BoxLayout(panel, BoxLayout.Y_AXIS));
        panel.setBorder(new EmptyBorder(5, 5, 5, 5));
        panel.setPreferredSize(new Dimension(80, 120));

        JLabel timeLabel = new JLabel(forecast.getFormattedTime());
        timeLabel.setFont(new Font(Font.SANS_SERIF, Font.BOLD, 11));
        timeLabel.setAlignmentX(Component.CENTER_ALIGNMENT);

        JLabel conditionLabel = new JLabel(forecast.getWeatherCondition());
        conditionLabel.setFont(new Font(Font.SANS_SERIF, Font.PLAIN, 9));
        conditionLabel.setAlignmentX(Component.CENTER_ALIGNMENT);

        JLabel tempLabel = new JLabel(PreferenceManager.formatTemperature(forecast.getTemperature()));
        tempLabel.setFont(new Font(Font.SANS_SERIF, Font.BOLD, 12));
        tempLabel.setAlignmentX(Component.CENTER_ALIGNMENT);

        JLabel humidityLabel = new JLabel(forecast.getHumidity() + "%");
        humidityLabel.setFont(new Font(Font.SANS_SERIF, Font.PLAIN, 9));
        humidityLabel.setAlignmentX(Component.CENTER_ALIGNMENT);

        panel.add(timeLabel);
        panel.add(Box.createVerticalStrut(3));
        panel.add(conditionLabel);
        panel.add(Box.createVerticalStrut(3));
        panel.add(tempLabel);
        panel.add(Box.createVerticalStrut(3));
        panel.add(humidityLabel);

        return panel;
    }

    private void toggleTheme() {
        boolean isDark = themeToggleButton.isSelected();

        try {
            String themeClassName;
            if (isDark) {
                themeClassName = "com.formdev.flatlaf.FlatDarkLaf";
                themeToggleButton.setText("Light");
                PreferenceManager.setTheme("dark");
            } else {
                themeClassName = "com.formdev.flatlaf.FlatLightLaf";
                themeToggleButton.setText("Dark");
                PreferenceManager.setTheme("light");
            }

            // Use reflection to load FlatLaf themes
            Class<?> themeClass = Class.forName(themeClassName);
            UIManager.setLookAndFeel((javax.swing.LookAndFeel) themeClass.getDeclaredConstructor().newInstance());

            SwingUtilities.updateComponentTreeUI(this);
            statusLabel.setText("Theme changed to " + (isDark ? "dark" : "light") + " mode");
        } catch (ClassNotFoundException e) {
            System.err.println("FlatLaf not available, cannot change theme");
            statusLabel.setText("FlatLaf not available - theme change disabled");
            themeToggleButton.setEnabled(false);
        } catch (Exception e) {
            System.err.println("Failed to change theme: " + e.getMessage());
            statusLabel.setText("Failed to change theme");
        }
    }

    private void toggleTemperatureUnit() {
        boolean isFahrenheit = unitToggleButton.isSelected();

        if (isFahrenheit) {
            PreferenceManager.setTemperatureUnit("fahrenheit");
            unitToggleButton.setText("°C");
        } else {
            PreferenceManager.setTemperatureUnit("celsius");
            unitToggleButton.setText("°F");
        }

        // Update all temperature displays
        refreshTemperatureDisplays();
        statusLabel.setText("Temperature unit changed to " + PreferenceManager.getTemperatureSymbol());
    }

    private void refreshTemperatureDisplays() {
        // This would update all visible temperature values
        // For now, just trigger a refresh of the current data
        if (currentCity != null && !currentCity.isEmpty()) {
            loadWeatherData(currentCity);
        }
    }

    private void setLoading(boolean loading) {
        loadingBar.setVisible(loading);
        searchButton.setEnabled(!loading);
        refreshButton.setEnabled(!loading);

        if (loading) {
            setCursor(Cursor.getPredefinedCursor(Cursor.WAIT_CURSOR));
        } else {
            setCursor(Cursor.getDefaultCursor());
        }
    }

    private void restoreWindowState() {
        int width = PreferenceManager.getWindowWidth();
        int height = PreferenceManager.getWindowHeight();
        setSize(width, height);

        int x = PreferenceManager.getWindowX();
        int y = PreferenceManager.getWindowY();

        if (x >= 0 && y >= 0) {
            setLocation(x, y);
        } else {
            setLocationRelativeTo(null); // Center on screen
        }
    }

    private void saveWindowState() {
        PreferenceManager.setWindowWidth(getWidth());
        PreferenceManager.setWindowHeight(getHeight());
        PreferenceManager.setWindowX(getX());
        PreferenceManager.setWindowY(getY());
    }

    // Public method to show API key configuration dialog
    public void showApiKeyDialog() {
        String apiKeyInfo = weatherService.getApiKeyInfo();

        if (!weatherService.isApiKeyConfigured()) {
            String message = "OpenWeatherMap API key is not configured.\n\n" +
                           "To get real weather data:\n" +
                           "1. Sign up at https://openweathermap.org/api\n" +
                           "2. Get your free API key\n" +
                           "3. Edit src/config.properties and replace YOUR_API_KEY_HERE with your key\n\n" +
                           "For now, the app will show mock data for demonstration.";

            JOptionPane.showMessageDialog(this, message, "API Key Configuration",
                                        JOptionPane.INFORMATION_MESSAGE);
        } else {
            // Test the API key
            String testResult = weatherService.testApiKey();
            if (!testResult.equals("API key is valid")) {
                String message = "API Key Status:\n\n" + apiKeyInfo + "\n\n" +
                               "There seems to be an issue with your API key.\n\n" +
                               "Common solutions:\n" +
                               "• Wait 10-15 minutes if you just created the key\n" +
                               "• Check if the key is correctly copied\n" +
                               "• Verify your OpenWeatherMap account is active\n" +
                               "• Check if you've exceeded free tier limits (1000 calls/day)\n\n" +
                               "The app will use mock data until the API key works.";

                JOptionPane.showMessageDialog(this, message, "API Key Issue",
                                            JOptionPane.WARNING_MESSAGE);
            }
        }
    }
}
