package model;

/**
 * Model class representing current weather data
 */
public class WeatherData {
    private String cityName;
    private String country;
    private double temperature;
    private double feelsLike;
    private int humidity;
    private double pressure;
    private double windSpeed;
    private int windDirection;
    private String weatherCondition;
    private String weatherDescription;
    private String iconCode;
    private long timestamp;
    private int visibility;
    private double uvIndex;

    // Constructors
    public WeatherData() {}

    public WeatherData(String cityName, String country, double temperature, 
                      String weatherCondition, String weatherDescription) {
        this.cityName = cityName;
        this.country = country;
        this.temperature = temperature;
        this.weatherCondition = weatherCondition;
        this.weatherDescription = weatherDescription;
        this.timestamp = System.currentTimeMillis();
    }

    // Getters and Setters
    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public double getTemperature() {
        return temperature;
    }

    public void setTemperature(double temperature) {
        this.temperature = temperature;
    }

    public double getFeelsLike() {
        return feelsLike;
    }

    public void setFeelsLike(double feelsLike) {
        this.feelsLike = feelsLike;
    }

    public int getHumidity() {
        return humidity;
    }

    public void setHumidity(int humidity) {
        this.humidity = humidity;
    }

    public double getPressure() {
        return pressure;
    }

    public void setPressure(double pressure) {
        this.pressure = pressure;
    }

    public double getWindSpeed() {
        return windSpeed;
    }

    public void setWindSpeed(double windSpeed) {
        this.windSpeed = windSpeed;
    }

    public int getWindDirection() {
        return windDirection;
    }

    public void setWindDirection(int windDirection) {
        this.windDirection = windDirection;
    }

    public String getWeatherCondition() {
        return weatherCondition;
    }

    public void setWeatherCondition(String weatherCondition) {
        this.weatherCondition = weatherCondition;
    }

    public String getWeatherDescription() {
        return weatherDescription;
    }

    public void setWeatherDescription(String weatherDescription) {
        this.weatherDescription = weatherDescription;
    }

    public String getIconCode() {
        return iconCode;
    }

    public void setIconCode(String iconCode) {
        this.iconCode = iconCode;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public int getVisibility() {
        return visibility;
    }

    public void setVisibility(int visibility) {
        this.visibility = visibility;
    }

    public double getUvIndex() {
        return uvIndex;
    }

    public void setUvIndex(double uvIndex) {
        this.uvIndex = uvIndex;
    }

    // Utility methods
    public String getFormattedLocation() {
        return cityName + (country != null && !country.isEmpty() ? ", " + country : "");
    }

    public String getWindDirectionText() {
        if (windDirection >= 337.5 || windDirection < 22.5) return "N";
        else if (windDirection < 67.5) return "NE";
        else if (windDirection < 112.5) return "E";
        else if (windDirection < 157.5) return "SE";
        else if (windDirection < 202.5) return "S";
        else if (windDirection < 247.5) return "SW";
        else if (windDirection < 292.5) return "W";
        else return "NW";
    }

    @Override
    public String toString() {
        return String.format("WeatherData{city='%s', temp=%.1f°, condition='%s'}", 
                           cityName, temperature, weatherCondition);
    }
}
