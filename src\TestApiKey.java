import service.WeatherService;

/**
 * Simple test program to verify API key functionality
 */
public class TestApi<PERSON>ey {
    public static void main(String[] args) {
        WeatherService service = new WeatherService();
        
        System.out.println("=== API Key Test ===");
        System.out.println(service.getApiKeyInfo());
        System.out.println();
        
        if (service.isApiKeyConfigured()) {
            System.out.println("Testing API key with London...");
            String testResult = service.testApiKey();
            System.out.println("Result: " + testResult);
            
            if (testResult.equals("API key is valid")) {
                System.out.println("\n✅ API key is working correctly!");
                
                // Try to get actual weather data
                try {
                    System.out.println("\nTesting weather data retrieval...");
                    var weatherData = service.getCurrentWeather("London");
                    System.out.println("✅ Successfully retrieved weather for: " + weatherData.getFormattedLocation());
                    System.out.println("Temperature: " + weatherData.getTemperature() + "°C");
                    System.out.println("Condition: " + weatherData.getWeatherDescription());
                } catch (Exception e) {
                    System.out.println("❌ Failed to retrieve weather data: " + e.getMessage());
                }
            } else {
                System.out.println("\n❌ API key test failed!");
                System.out.println("The application will use mock data.");
            }
        } else {
            System.out.println("❌ API key not configured");
        }
    }
}
