@echo off
echo ========================================
echo    Weather App - Clean Compilation
echo ========================================
echo.

REM Clean previous compilation
if exist bin\*.class (
    echo Cleaning previous compilation...
    del /Q bin\*.class >nul 2>&1
    del /Q bin\model\*.class >nul 2>&1
    del /Q bin\service\*.class >nul 2>&1
    del /Q bin\ui\*.class >nul 2>&1
    del /Q bin\utils\*.class >nul 2>&1
)

REM Compile with clean output
echo Compiling Java sources...
javac -cp "lib/flatlaf-3.6.jar;src" -Xlint:-deprecation -d bin src/Main.java src/ui/WeatherUI.java src/model/*.java src/service/WeatherService.java src/utils/PreferenceManager.java src/TestApiKey.java

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Compilation failed!
    pause
    exit /b 1
)

REM Copy configuration
echo Copying configuration file...
copy src\config.properties bin\ >nul 2>&1

echo.
echo ✅ Compilation successful!
echo.
echo Available commands:
echo   java -cp "lib/flatlaf-3.6.jar;bin" Main        - Run Weather App
echo   java -cp "lib/flatlaf-3.6.jar;bin" TestApiKey  - Test API Key
echo.
pause
