package model;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * Model class representing daily weather forecast
 */
public class Forecast {
    private LocalDate date;
    private double minTemperature;
    private double maxTemperature;
    private String weatherCondition;
    private String weatherDescription;
    private String iconCode;
    private int humidity;
    private double windSpeed;
    private double pressure;
    private double pop; // Probability of precipitation

    // Constructors
    public Forecast() {}

    public Forecast(LocalDate date, double minTemperature, double maxTemperature, 
                   String weatherCondition, String weatherDescription) {
        this.date = date;
        this.minTemperature = minTemperature;
        this.maxTemperature = maxTemperature;
        this.weatherCondition = weatherCondition;
        this.weatherDescription = weatherDescription;
    }

    // Getters and Setters
    public LocalDate getDate() {
        return date;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public double getMinTemperature() {
        return minTemperature;
    }

    public void setMinTemperature(double minTemperature) {
        this.minTemperature = minTemperature;
    }

    public double getMaxTemperature() {
        return maxTemperature;
    }

    public void setMaxTemperature(double maxTemperature) {
        this.maxTemperature = maxTemperature;
    }

    public String getWeatherCondition() {
        return weatherCondition;
    }

    public void setWeatherCondition(String weatherCondition) {
        this.weatherCondition = weatherCondition;
    }

    public String getWeatherDescription() {
        return weatherDescription;
    }

    public void setWeatherDescription(String weatherDescription) {
        this.weatherDescription = weatherDescription;
    }

    public String getIconCode() {
        return iconCode;
    }

    public void setIconCode(String iconCode) {
        this.iconCode = iconCode;
    }

    public int getHumidity() {
        return humidity;
    }

    public void setHumidity(int humidity) {
        this.humidity = humidity;
    }

    public double getWindSpeed() {
        return windSpeed;
    }

    public void setWindSpeed(double windSpeed) {
        this.windSpeed = windSpeed;
    }

    public double getPressure() {
        return pressure;
    }

    public void setPressure(double pressure) {
        this.pressure = pressure;
    }

    public double getPop() {
        return pop;
    }

    public void setPop(double pop) {
        this.pop = pop;
    }

    // Utility methods
    public String getFormattedDate() {
        return date.format(DateTimeFormatter.ofPattern("MMM dd"));
    }

    public String getDayOfWeek() {
        return date.format(DateTimeFormatter.ofPattern("EEEE"));
    }

    public String getTemperatureRange() {
        return String.format("%.0f° / %.0f°", minTemperature, maxTemperature);
    }

    @Override
    public String toString() {
        return String.format("Forecast{date=%s, min=%.1f°, max=%.1f°, condition='%s'}", 
                           date, minTemperature, maxTemperature, weatherCondition);
    }
}
