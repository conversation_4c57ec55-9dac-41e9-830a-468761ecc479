import javax.swing.SwingUtilities;
import javax.swing.UIManager;
import ui.WeatherUI;

/**
 * Main class for the Weather Application
 * Sets up FlatLaf Look and Feel and launches the UI
 */
public class Main {
    public static void main(String[] args) {
        // Set FlatLaf Look and Feel
        try {
            // Try to load FlatLaf using reflection to avoid compilation dependency
            Class<?> flatLafClass = Class.forName("com.formdev.flatlaf.FlatLightLaf");
            UIManager.setLookAndFeel((javax.swing.LookAndFeel) flatLafClass.getDeclaredConstructor().newInstance());
            System.out.println("FlatLaf Look and Feel loaded successfully");
        } catch (Exception e) {
            System.err.println("FlatLaf not available, using system Look and Feel");
            try {
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
            } catch (Exception ex) {
                System.err.println("Failed to set system Look and Feel: " + ex.getMessage());
            }
        }

        // Launch the application on the Event Dispatch Thread
        SwingUtilities.invokeLater(() -> {
            try {
                WeatherUI weatherUI = new WeatherUI();
                weatherUI.setVisible(true);

                // Show API key configuration dialog if needed
                SwingUtilities.invokeLater(() -> weatherUI.showApiKeyDialog());
            } catch (Exception e) {
                System.err.println("Failed to launch Weather Application");
                e.printStackTrace();
            }
        });
    }
}
