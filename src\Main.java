import com.formdev.flatlaf.FlatLightLaf;
import ui.WeatherUI;

import javax.swing.SwingUtilities;
import javax.swing.UIManager;

/**
 * Main class for the Weather Application
 * Sets up FlatLaf Look and Feel and launches the UI
 */
public class Main {
    public static void main(String[] args) {
        // Set FlatLaf Look and Feel
        try {
            UIManager.setLookAndFeel(new FlatLightLaf());
        } catch (Exception e) {
            System.err.println("Failed to initialize FlatLaf Look and Feel");
            e.printStackTrace();
        }

        // Launch the application on the Event Dispatch Thread
        SwingUtilities.invokeLater(() -> {
            try {
                WeatherUI weatherUI = new WeatherUI();
                weatherUI.setVisible(true);

                // Show API key configuration dialog if needed
                SwingUtilities.invokeLater(() -> weatherUI.showApiKeyDialog());
            } catch (Exception e) {
                System.err.println("Failed to launch Weather Application");
                e.printStackTrace();
            }
        });
    }
}
