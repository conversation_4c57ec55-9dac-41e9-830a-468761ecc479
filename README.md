# 🌤️ Application Météo Moderne - Java Swing + FlatLaf

Une application de bureau Java moderne pour afficher la météo actuelle, les prévisions sur 5 jours et les prévisions horaires. Interface utilisateur construite avec Swing et stylisée avec FlatLaf.

## ✨ Fonctionnalités

- **Météo actuelle** : Température, conditions météo, humidité, vent, pression
- **Prévisions 5 jours** : Températures min/max, conditions météo
- **Prévisions horaires** : Prévisions détaillées sur 24h
- **Recherche de ville** : Barre de recherche pour changer de ville
- **Thèmes modernes** : Basculer entre thème clair et sombre (FlatLaf)
- **Unités de température** : Basculer entre Celsius et Fahrenheit
- **Préférences persistantes** : Sauvegarde automatique des paramètres
- **Interface responsive** : Design moderne et adaptatif

## 🚀 Démarrage rapide

### Prérequis
- Java 8 ou plus récent
- Windows (testé sur Windows, adaptable pour Linux/macOS)

### Installation et exécution

1. **Cloner ou télécharger le projet**
2. **Exécuter l'application** :
   ```bash
   # Méthode simple (Windows)
   run.bat

   # Ou manuellement
   javac -cp "lib/flatlaf-3.6.jar;src" src/Main.java src/ui/WeatherUI.java src/model/*.java src/service/WeatherService.java src/utils/PreferenceManager.java -d bin
   copy src\config.properties bin\
   java -cp "lib/flatlaf-3.6.jar;bin" Main
   ```

### Configuration de l'API (optionnel)

L'application fonctionne avec des données de démonstration par défaut. Pour obtenir des données météo réelles :

1. **Créer un compte** sur [OpenWeatherMap](https://openweathermap.org/api)
2. **Obtenir une clé API gratuite**
3. **Modifier** `src/config.properties` :
   ```properties
   api.key=VOTRE_CLE_API_ICI
   ```

## 📁 Structure du projet

```
Meteo_oop/
├── src/
│   ├── Main.java                    # Point d'entrée de l'application
│   ├── ui/
│   │   └── WeatherUI.java          # Interface graphique principale
│   ├── model/
│   │   ├── WeatherData.java        # Modèle des données météo actuelles
│   │   ├── Forecast.java           # Modèle des prévisions journalières
│   │   └── HourlyForecast.java     # Modèle des prévisions horaires
│   ├── service/
│   │   └── WeatherService.java     # Service API OpenWeatherMap
│   ├── utils/
│   │   └── PreferenceManager.java  # Gestion des préférences utilisateur
│   └── config.properties           # Configuration de l'application
├── lib/
│   └── flatlaf-3.6.jar            # Bibliothèque FlatLaf Look and Feel
├── bin/                            # Fichiers compilés
├── run.bat                         # Script de lancement Windows
└── README.md                       # Ce fichier
```

## 🎨 Captures d'écran

L'application propose :
- **Thème clair** : Interface moderne et épurée
- **Thème sombre** : Confort visuel pour les environnements sombres
- **Design responsive** : Adaptation automatique à la taille de la fenêtre
- **Icônes et couleurs** : Interface intuitive et professionnelle

## 🔧 Technologies utilisées

- **Java 8+** : Langage de programmation
- **Swing** : Framework d'interface graphique
- **FlatLaf 3.6** : Look and Feel moderne
- **OpenWeatherMap API** : Données météo en temps réel
- **Java Preferences API** : Sauvegarde des préférences

## 📋 Fonctionnalités détaillées

### Interface principale
- Barre de recherche pour sélectionner une ville
- Affichage de la météo actuelle avec tous les détails
- Panneau des prévisions sur 5 jours
- Défilement horizontal des prévisions horaires

### Contrôles
- **Bouton Recherche** : Rechercher une nouvelle ville
- **Bouton Actualiser** : Mettre à jour les données
- **Basculer le thème** : Passer du mode clair au mode sombre
- **Basculer l'unité** : Changer entre Celsius et Fahrenheit

### Données affichées
- **Température actuelle** et ressentie
- **Conditions météo** (ensoleillé, nuageux, pluvieux, etc.)
- **Humidité** en pourcentage
- **Vitesse et direction du vent**
- **Pression atmosphérique**
- **Prévisions détaillées** sur 5 jours et 24 heures

## 🛠️ Développement

### Architecture
L'application suit les principes de la programmation orientée objet :
- **Séparation des responsabilités** : UI, Service, Model, Utils
- **Encapsulation** : Classes avec getters/setters appropriés
- **Modularité** : Code organisé en packages logiques
- **Gestion d'erreurs** : Try-catch et messages d'erreur utilisateur

### Extensibilité
- Facile d'ajouter de nouveaux thèmes FlatLaf
- Support pour d'autres APIs météo
- Possibilité d'ajouter des graphiques et charts
- Internationalisation possible

## 📝 Notes

- L'application utilise des données de démonstration si aucune clé API n'est configurée
- Les préférences sont sauvegardées automatiquement
- Compatible avec Java 8+ et tous les systèmes d'exploitation
- Interface entièrement en français avec support multilingue possible

## 🤝 Contribution

Ce projet est un exemple d'application météo moderne en Java. N'hésitez pas à :
- Signaler des bugs
- Proposer des améliorations
- Ajouter de nouvelles fonctionnalités
- Améliorer l'interface utilisateur

---

**Développé avec ❤️ en Java + Swing + FlatLaf**
